import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { AppButton } from 'wini-mobile-components';
import { ColorThemes } from '../../../assets/skin/colors';
import RecipientSelector from '../../../components/RecipientSelector';
import PointAmountInput from '../../../components/PointAmountInput';
import CurrentUserInfo from '../../../components/CurrentUserInfo';

interface Step1TransferInfoProps {
  currentPoints: number;
  transferAmount: string;
  recipientName?: string;
  recipientPhone?: string;
  userName: string;
  onAmountChange: (amount: string) => void;
  onSelectRecipient: () => void;
  onNext: () => void;
}

const Step1TransferInfo: React.FC<Step1TransferInfoProps> = ({
  currentPoints,
  transferAmount,
  recipientName,
  recipientPhone,
  userName,
  onAmountChange,
  onSelectRecipient,
  onNext,
}) => {
  const isValidAmount = () => {
    const amount = parseInt(transferAmount || '0');
    return amount > 0 && amount <= currentPoints;
  };

  const isFormValid = () => {
    return recipientName && recipientPhone && isValidAmount();
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <CurrentUserInfo
          userName={userName}
          currentPoints={currentPoints}
        />

        <RecipientSelector
          recipientName={recipientName}
          recipientPhone={recipientPhone}
          onPress={onSelectRecipient}
        />

        <PointAmountInput
          currentPoints={currentPoints}
          transferAmount={transferAmount}
          onAmountChange={onAmountChange}
        />
      </ScrollView>

      <View style={styles.buttonContainer}>
        <AppButton
          title="Tiếp theo"
          onPress={onNext}
          disabled={!isFormValid()}
          backgroundColor={
            isFormValid() 
              ? ColorThemes.light.primary_main_color 
              : ColorThemes.light.neutral_lighter_border_color
          }
          titleColor={ColorThemes.light.white}
          containerStyle={styles.button}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  content: {
    flex: 1,
    paddingTop: 16,
  },
  buttonContainer: {
    padding: 16,
    backgroundColor: ColorThemes.light.white,
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.neutral_lighter_border_color,
  },
  button: {
    borderRadius: 24,
    height: 48,
  },
});

export default Step1TransferInfo;
