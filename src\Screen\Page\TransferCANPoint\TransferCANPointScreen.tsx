import React, { useState, useRef } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import PagerView from 'react-native-pager-view';
import { useNavigation } from '@react-navigation/native';
import { showSnackbar, ComponentStatus } from 'wini-mobile-components';
import TitleWithBackAction from '../../Layout/titleWithBackAction';
import StepIndicator from '../../../components/StepIndicator';
import Step1TransferInfo from './Step1TransferInfo';
import Step2OTPVerification from './Step2OTPVerification';
import Step3TransactionDetail from './Step3TransactionDetail';
import { ColorThemes } from '../../../assets/skin/colors';
import { useSelectorCustomerState } from '../../../redux/hook/customerHook';

const { width } = Dimensions.get('window');

const TransferCANPointScreen: React.FC = () => {
  const navigation = useNavigation();
  const pagerRef = useRef<PagerView>(null);
  const customer = useSelectorCustomerState().data;
  
  // State management
  const [currentStep, setCurrentStep] = useState(1);
  const [transferAmount, setTransferAmount] = useState('');
  const [recipientName, setRecipientName] = useState('');
  const [recipientPhone, setRecipientPhone] = useState('');
  const [otpValue, setOtpValue] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [transactionData, setTransactionData] = useState<any>(null);

  // Mock current points - replace with actual data
  const currentPoints = 1500000;

  const handleBack = () => {
    if (currentStep > 1) {
      const prevStep = currentStep - 1;
      setCurrentStep(prevStep);
      pagerRef.current?.setPage(prevStep - 1);
    } else {
      navigation.goBack();
    }
  };

  const handleSelectRecipient = () => {
    // Mock recipient selection - replace with actual recipient picker
    setRecipientName('Nguyễn Đức Long');
    setRecipientPhone('0983***135');
    
    showSnackbar({
      message: 'Đã chọn người nhận',
      status: ComponentStatus.SUCCSESS,
    });
  };

  const handleStep1Next = () => {
    setCurrentStep(2);
    pagerRef.current?.setPage(1);
    
    // Mock send OTP
    showSnackbar({
      message: 'Mã OTP đã được gửi',
      status: ComponentStatus.SUCCSESS,
    });
  };

  const handleOTPComplete = (otp: string) => {
    setOtpValue(otp);
  };

  const handleResendOTP = () => {
    setOtpValue('');
    showSnackbar({
      message: 'Đã gửi lại mã OTP',
      status: ComponentStatus.SUCCSESS,
    });
  };

  const handleVerifyOTP = async () => {
    setIsVerifying(true);
    
    // Mock OTP verification
    setTimeout(() => {
      setIsVerifying(false);
      
      // Mock successful transaction
      const mockTransactionData = {
        status: 'success' as const,
        transactionId: 'ASDANCJK91',
        amount: parseInt(transferAmount),
        recipientName,
        recipientPhone,
        senderName: customer?.Name || 'Nguyễn Thanh Tùng',
        timestamp: new Date().toISOString(),
      };
      
      setTransactionData(mockTransactionData);
      setCurrentStep(3);
      pagerRef.current?.setPage(2);
      
      showSnackbar({
        message: 'Chuyển CAN Point thành công!',
        status: ComponentStatus.SUCCSESS,
      });
    }, 2000);
  };

  const handleDone = () => {
    navigation.goBack();
  };

  return (
    <TitleWithBackAction
      onBack={handleBack}
      titleBottom="Ví của tôi"
    >
      <View style={styles.container}>
        <StepIndicator currentStep={currentStep} totalSteps={3} />
        
        <PagerView
          ref={pagerRef}
          style={styles.pagerView}
          initialPage={0}
          scrollEnabled={false}
        >
          <View key="step1" style={styles.pageContainer}>
            <Step1TransferInfo
              currentPoints={currentPoints}
              transferAmount={transferAmount}
              recipientName={recipientName}
              recipientPhone={recipientPhone}
              userName={customer?.Name || 'Nguyễn Thanh Tùng'}
              onAmountChange={setTransferAmount}
              onSelectRecipient={handleSelectRecipient}
              onNext={handleStep1Next}
            />
          </View>

          <View key="step2" style={styles.pageContainer}>
            <Step2OTPVerification
              phoneNumber={recipientPhone}
              isVerifying={isVerifying}
              onOTPComplete={handleOTPComplete}
              onResendOTP={handleResendOTP}
              onVerify={handleVerifyOTP}
              otpValue={otpValue}
            />
          </View>

          <View key="step3" style={styles.pageContainer}>
            {transactionData && (
              <Step3TransactionDetail
                transactionData={transactionData}
                onDone={handleDone}
              />
            )}
          </View>
        </PagerView>
      </View>
    </TitleWithBackAction>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  pagerView: {
    flex: 1,
  },
  pageContainer: {
    flex: 1,
    width: width,
  },
});

export default TransferCANPointScreen;
