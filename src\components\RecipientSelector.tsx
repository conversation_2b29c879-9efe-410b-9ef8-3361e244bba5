import React from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';
import { Winicon } from 'wini-mobile-components';
import { ColorThemes } from '../assets/skin/colors';
import { TypoSkin } from '../assets/skin/typography';

interface RecipientSelectorProps {
  recipientName?: string;
  recipientPhone?: string;
  onPress: () => void;
}

const RecipientSelector: React.FC<RecipientSelectorProps> = ({
  recipientName,
  recipientPhone,
  onPress,
}) => {
  return (
    <TouchableOpacity style={styles.container} onPress={onPress} activeOpacity={0.7}>
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <Winicon 
            src="outline/users/profile" 
            size={24} 
            color={ColorThemes.light.primary_main_color} 
          />
        </View>
        <View style={styles.textContainer}>
          <Text style={styles.label}>Người nhận</Text>
          {recipientName && recipientPhone ? (
            <>
              <Text style={styles.recipient}>{recipientName}</Text>
              <Text style={styles.phone}>{recipientPhone}</Text>
            </>
          ) : (
            <Text style={styles.placeholder}>Chọn người nhận</Text>
          )}
        </View>
        <Winicon 
          src="outline/arrows/chevron-right" 
          size={20} 
          color={ColorThemes.light.neutral_text_subtitle_color} 
        />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: ColorThemes.light.primary_background,
    borderRadius: 12,
    marginHorizontal: 16,
    marginVertical: 8,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    gap: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: ColorThemes.light.white,
    alignItems: 'center',
    justifyContent: 'center',
  },
  textContainer: {
    flex: 1,
  },
  label: {
    ...TypoSkin.label3,
    color: ColorThemes.light.primary_main_color,
    marginBottom: 4,
  },
  recipient: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: '600',
  },
  phone: {
    ...TypoSkin.regular3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginTop: 2,
  },
  placeholder: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
});

export default RecipientSelector;
