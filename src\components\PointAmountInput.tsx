import React, { useState } from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';
import { TextField } from 'wini-mobile-components';
import { ColorThemes } from '../assets/skin/colors';
import { <PERSON><PERSON><PERSON>kin } from '../assets/skin/typography';

interface PointAmountInputProps {
  currentPoints: number;
  transferAmount: string;
  onAmountChange: (amount: string) => void;
}

const PointAmountInput: React.FC<PointAmountInputProps> = ({
  currentPoints,
  transferAmount,
  onAmountChange,
}) => {
  const quickAmounts = [100, 200, 500, 1000, 5000, 10000];

  const handleQuickSelect = (amount: number) => {
    onAmountChange(amount.toString());
  };

  const formatNumber = (num: number) => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  const formatInputValue = (value: string) => {
    // Remove all non-numeric characters
    const numericValue = value.replace(/[^0-9]/g, '');
    // Format with commas
    return numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  const handleInputChange = (value: string) => {
    const numericValue = value.replace(/[^0-9]/g, '');
    onAmountChange(numericValue);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>Point chuyển</Text>
      
      <View style={styles.inputContainer}>
        <TextField
          value={formatInputValue(transferAmount)}
          onChangeText={handleInputChange}
          placeholder="0"
          keyboardType="numeric"
          style={styles.input}
          textStyle={styles.inputText}
        />
        <TouchableOpacity
          style={styles.maxButton}
          onPress={() => onAmountChange(currentPoints.toString())}
        >
          <Text style={styles.maxButtonText}>MAX</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.quickSelectContainer}>
        {quickAmounts.map((amount) => (
          <TouchableOpacity
            key={amount}
            style={styles.quickSelectButton}
            onPress={() => handleQuickSelect(amount)}
            activeOpacity={0.7}
          >
            <Text style={styles.quickSelectText}>{formatNumber(amount)}</Text>
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.currentPointsContainer}>
        <Text style={styles.currentPointsLabel}>CPoint của tôi</Text>
        <Text style={styles.currentPointsValue}>{formatNumber(currentPoints)}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  label: {
    ...TypoSkin.label3,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 12,
  },
  inputContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  input: {
    backgroundColor: ColorThemes.light.white,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_lighter_border_color,
    paddingRight: 60,
  },
  inputText: {
    ...TypoSkin.heading4,
    color: ColorThemes.light.neutral_text_title_color,
    textAlign: 'left',
  },
  maxButton: {
    position: 'absolute',
    right: 12,
    top: '50%',
    transform: [{ translateY: -12 }],
    backgroundColor: ColorThemes.light.secondary1_main_color,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 4,
  },
  maxButtonText: {
    ...TypoSkin.label4,
    color: ColorThemes.light.white,
    fontWeight: 'bold',
  },
  quickSelectContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  quickSelectButton: {
    backgroundColor: ColorThemes.light.primary_background,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: ColorThemes.light.primary_border_color,
  },
  quickSelectText: {
    ...TypoSkin.regular3,
    color: ColorThemes.light.primary_main_color,
  },
  currentPointsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.neutral_lighter_border_color,
  },
  currentPointsLabel: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  currentPointsValue: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.primary_main_color,
  },
});

export default PointAmountInput;
